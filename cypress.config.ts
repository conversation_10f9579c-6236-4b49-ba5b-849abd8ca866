export default {
  e2e: {
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },

    defaultCommandTimeout: 30000, // 30 seconds for commands
    pageLoadTimeout: 30000, // 30 seconds for page loads
    requestTimeout: 30000, // 30 seconds for HTTP requests
    responseTimeout: 30000, // 30 seconds for server responses
    viewportWidth: 1600,
    viewportHeight: 1080,
  },
}
