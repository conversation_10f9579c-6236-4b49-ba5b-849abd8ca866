import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'

const renderCarousel = (props = {}, items = ['1', '2', '3']) => {
  return render(
    <Carousel {...props}>
      <CarouselContent>
        {items.map((text, i) => (
          <CarouselItem key={i}>{text}</CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  )
}

describe('Carousel Rendering & Snapshots', () => {
  it('should render basic carousel with multiple slides', () => {
    const { container } = renderCarousel()
    expect(container).toMatchSnapshot()
  })

  it('should render carousel with navigation buttons', () => {
    const { container } = renderCarousel()

    const prev = screen.getByRole('button', { name: /previous/i })
    const next = screen.getByRole('button', { name: /next/i })

    expect(prev).toBeInTheDocument()
    expect(next).toBeInTheDocument()
    expect(container).toMatchSnapshot()
  })

  it('should render with custom className', () => {
    const { container } = render(
      <Carousel className="my-carousel">
        <CarouselContent>
          <CarouselItem>Slide</CarouselItem>
        </CarouselContent>
      </Carousel>
    )
    expect(container).toMatchSnapshot()
  })

  it('should render carousel with loop option enabled', () => {
    const { container } = renderCarousel({ opts: { loop: true } }, ['1', '2'])
    expect(container).toMatchSnapshot()
  })

  it('should render with no slides', () => {
    const { container } = render(
      <Carousel>
        <CarouselContent></CarouselContent>
      </Carousel>
    )
    expect(container).toMatchSnapshot()
    expect(screen.queryAllByRole('group')).toHaveLength(0)
  })

  it('should render large number of slides without crashing', () => {
    const manySlides = Array.from({ length: 50 }, (_, i) => `${i + 1}`)
    const { container } = renderCarousel({}, manySlides)
    expect(container).toMatchSnapshot()
    expect(screen.getAllByRole('group')).toHaveLength(50)
  })
})

describe('Carousel Navigation', () => {
  it('should render all slides', () => {
    renderCarousel()
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
  })

  it('should disable navigation buttons when only one slide exists', () => {
    renderCarousel({}, ['Only Slide'])

    const prev = screen.getByRole('button', { name: /previous/i })
    const next = screen.getByRole('button', { name: /next/i })

    expect(prev).toBeDisabled()
    expect(next).toBeDisabled()
  })

  it('should navigate forward and backward via buttons', async () => {
    const user = userEvent.setup()
    renderCarousel({}, ['First', 'Second', 'Third'])

    const next = screen.getByRole('button', { name: /next/i })
    const prev = screen.getByRole('button', { name: /previous/i })

    await user.click(next)
    await waitFor(() => {
      expect(screen.getByText('Second')).toBeVisible()
    })

    await user.click(next)
    await waitFor(() => {
      expect(screen.getByText('Third')).toBeVisible()
    })

    await user.click(prev)
    await waitFor(() => {
      expect(screen.getByText('Second')).toBeVisible()
    })
  })

  it('should disable prev button on first slide', () => {
    renderCarousel({}, ['1', '2'])

    const prev = screen.getByRole('button', { name: /previous/i })
    expect(prev).toBeDisabled()
  })

  it('should disable next button on last slide if not looping', async () => {
    const user = userEvent.setup()
    renderCarousel({}, ['1', '2'])

    const next = screen.getByRole('button', { name: /next/i })

    await user.click(next)
    await waitFor(() => {
      expect(screen.getByText('2')).toBeVisible()
      expect(next).toBeDisabled()
    })
  })

  it('should support loop mode (no disabled next button)', async () => {
    const user = userEvent.setup()
    renderCarousel({ opts: { loop: true } }, ['1', '2'])

    const next = screen.getByRole('button', { name: /next/i })

    await user.click(next)
    await waitFor(() => {
      expect(screen.getByText('2')).toBeVisible()
    })

    await user.click(next)
    await waitFor(() => {
      expect(screen.getByText('1')).toBeVisible()
    })
  })
})

describe('Carousel Accessibility & Keyboard', () => {
  it('should navigate with keyboard (Enter key)', async () => {
    const user = userEvent.setup()
    renderCarousel({}, ['1', '2'])

    const next = screen.getByRole('button', { name: /next/i })
    next.focus()
    await user.keyboard('{Enter}')
    await waitFor(() => {
      expect(screen.getByText('2')).toBeVisible()
    })
  })

  it('should navigate with ArrowRight and ArrowLeft keys', async () => {
    const user = userEvent.setup()
    renderCarousel({}, ['Slide 1', 'Slide 2', 'Slide 3'])

    const next = screen.getByRole('button', { name: /next/i })
    next.focus()

    await user.keyboard('{ArrowRight}')
    await waitFor(() => {
      expect(screen.getByText('Slide 2')).toBeVisible()
    })

    await user.keyboard('{ArrowRight}')
    await waitFor(() => {
      expect(screen.getByText('Slide 3')).toBeVisible()
    })

    await user.keyboard('{ArrowLeft}')
    await waitFor(() => {
      expect(screen.getByText('Slide 2')).toBeVisible()
    })
  })

  it('should pass carousel API via setApi callback', () => {
    const setApi = jest.fn()
    render(
      <Carousel setApi={setApi}>
        <CarouselContent>
          <CarouselItem>Only</CarouselItem>
        </CarouselContent>
      </Carousel>
    )
    expect(setApi).toHaveBeenCalled()
    const api = setApi.mock.calls[0][0]
    expect(typeof api.scrollNext).toBe('function')
  })

  it('should have accessibility roles and labels', () => {
    renderCarousel({}, ['1'])

    const region = screen.getByRole('region')
    expect(region).toHaveAttribute('aria-roledescription', 'carousel')

    const slides = screen.getAllByRole('group')
    slides.forEach(slide => {
      expect(slide).toHaveAttribute('aria-roledescription', 'slide')
    })
  })

  it('should accept custom aria-label for region', () => {
    render(
      <Carousel aria-label="Custom Carousel Label">
        <CarouselContent>
          <CarouselItem>1</CarouselItem>
        </CarouselContent>
      </Carousel>
    )
    const region = screen.getByRole('region')
    expect(region).toHaveAttribute('aria-label', 'Custom Carousel Label')
  })
})
