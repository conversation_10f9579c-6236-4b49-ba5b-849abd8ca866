describe('Homepage Layout', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000')
  })

  describe('Content Section', () => {
    it('should render all content sections', () => {
      cy.get('.grid-in-hero').should('exist')
      cy.get('.grid-in-roadblocks').should('exist')
      cy.get('.grid-in-ascension-logs').should('exist')
      cy.get('.grid-in-application-development-intro').should('exist')
      cy.get('.grid-in-cards-plan').should('exist')
      cy.get('.grid-in-service-accordion').should('exist')
    })
  })

  describe('Carousel Section', () => {
    it('should render the carousel section with correct titles', () => {
      cy.get('.grid-in-carousel').should('exist')

      cy.get('.grid-in-carousel h2').should('have.length', 3)
      cy.get('.grid-in-carousel [aria-roledescription="carousel"]').should('have.length', 3)
      cy.get('.grid-in-carousel h2').should('contain.text', 'What we love to do:')
      cy.get('.grid-in-carousel h2').should('contain.text', 'Building Dreams The Manystack Way')
      cy.get('.grid-in-carousel h2').should('contain.text', 'Dreams Already Crafted')
    })
  })
})
