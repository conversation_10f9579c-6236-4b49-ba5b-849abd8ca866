import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'

// Mock embla-carousel-react - NEEDED for CarouselCard component
jest.mock('embla-carousel-react', () => ({
  __esModule: true,
  default: jest.fn(() => [
    jest.fn(),
    {
      canScrollPrev: jest.fn(() => true),
      canScrollNext: jest.fn(() => true),
      scrollPrev: jest.fn(),
      scrollNext: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
    },
  ]),
}))

const renderCarouselCard = (props = {}, children = <div>Default Child</div>) =>
  render(<CarouselCard {...props}>{children}</CarouselCard>)

describe('CarouselCard Component', () => {
  describe('Basic rendering', () => {
    it('should render with single child and matches snapshot', () => {
      const { container } = renderCarouselCard({}, <div>Single Child Content</div>)
      expect(container).toMatchSnapshot()
    })

    it('should render with multiple children and matches snapshot', () => {
      const children = (
        <>
          <div>Child 1</div>
          <div>Child 2</div>
          <div>Child 3</div>
        </>
      )
      const { container } = renderCarouselCard({}, children)
      expect(container).toMatchSnapshot()
    })
  })

  describe('With title', () => {
    it('should render with title and single child', () => {
      const { container } = renderCarouselCard(
        { title: 'Test Carousel Title' },
        <div>Content with title</div>
      )
      expect(container).toMatchSnapshot()
    })

    it('should render with title and multiple children', () => {
      const children = (
        <>
          <div>Item 1</div>
          <div>Item 2</div>
          <div>Item 3</div>
        </>
      )
      const { container } = renderCarouselCard({ title: 'Multiple Items Carousel' }, children)
      expect(container).toMatchSnapshot()
    })
  })

  describe('Without title', () => {
    it('should render without title', () => {
      const children = (
        <>
          <div>Content without title</div>
          <div>Another item</div>
        </>
      )
      const { container } = renderCarouselCard({}, children)
      expect(container).toMatchSnapshot()
    })
  })

  describe('With overlay', () => {
    it('should render with overlay only', () => {
      const overlay = (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <span className="text-white">Overlay Content</span>
        </div>
      )
      const children = (
        <>
          <div>Content with overlay</div>
          <div>Another item</div>
        </>
      )
      const { container } = renderCarouselCard({ overlay }, children)
      expect(container).toMatchSnapshot()
    })

    it('should render with title and overlay', () => {
      const overlay = (
        <div className="absolute top-0 right-0 p-2">
          <button>Close</button>
        </div>
      )
      const children = (
        <>
          <div>Item 1</div>
          <div>Item 2</div>
        </>
      )
      const { container } = renderCarouselCard(
        { title: 'Carousel with Overlay', overlay },
        children
      )
      expect(container).toMatchSnapshot()
    })
  })

  describe('With custom className', () => {
    it('should render with custom className and title', () => {
      const children = (
        <>
          <div>Item 1</div>
          <div>Item 2</div>
        </>
      )
      const { container } = renderCarouselCard(
        { className: 'w-full h-96', title: 'Custom Styled Carousel' },
        children
      )
      expect(container).toMatchSnapshot()
    })
  })

  describe('Navigation visibility', () => {
    it('should not show navigation controls with single child', () => {
      const { container } = renderCarouselCard({}, <div>Single item - no nav needed</div>)
      expect(container).toMatchSnapshot()
    })

    it('should show navigation controls with multiple children', () => {
      const children = (
        <>
          <div>Item 1</div>
          <div>Item 2</div>
        </>
      )
      const { container } = renderCarouselCard({}, children)
      expect(container).toMatchSnapshot()
    })
  })

  describe('Event handlers', () => {
    it('should call onMouseEnter and onMouseLeave handlers', () => {
      const mockMouseEnter = jest.fn()
      const mockMouseLeave = jest.fn()

      const { container } = renderCarouselCard(
        { onMouseEnter: mockMouseEnter, onMouseLeave: mockMouseLeave },
        <>
          <div>Content with event handlers</div>
          <div>Another item</div>
        </>
      )

      // The root of Carousel is the container’s first child
      const carousel = container.firstChild

      if (carousel) {
        fireEvent.mouseEnter(carousel)
        expect(mockMouseEnter).toHaveBeenCalledTimes(1)

        fireEvent.mouseLeave(carousel)
        expect(mockMouseLeave).toHaveBeenCalledTimes(1)
      }
    })
  })

  describe('setApi callback', () => {
    it('should call setApi when provided', () => {
      const setApi = jest.fn()
      renderCarouselCard({ setApi })

      expect(setApi).toHaveBeenCalled()
      const api = setApi.mock.calls[0][0]
      expect(api).toHaveProperty('scrollNext')
      expect(typeof api.scrollNext).toBe('function')
    })
  })
})
